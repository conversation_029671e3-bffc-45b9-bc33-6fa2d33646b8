import Taro from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import { Button } from '@nutui/nutui-react-taro';
import { AddOnShopPromotion } from '../../type';
import { useCallback, useMemo } from 'react';
import classNames from 'classnames';
import styles from './index.module.scss';
import { clickOperateReport } from '@/common/mallTracking';
import { getImage } from '@/hooks/useImage';
import { fenToYuanSmart } from '@/utils/price';

type AddPromotionBarProps = {
  isTab: boolean;
  data: AddOnShopPromotion;
  onClick: () => void;
  diffAmt?: number;
  cartDeliveryType?: number;
  onChangeCartType?: () => any;
  collectData: any; // 下一阶段优惠信息
};

const PromotionUnitMap = ['', '元', '件'];

const AddPromotionBar: React.FC<AddPromotionBarProps> = ({
  data,
  onClick,
  isTab,
  diffAmt = 0,
  cartDeliveryType = 0,
  onChangeCartType,
  collectData,
}) => {
  console.log('data === ', data);

  const handleClick = useCallback(() => {
    if (data?.calcPromotionInfoVO?.promotionId) {
      const { promotionId, doorSillRemain, description, tag, tagText } =
        data.calcPromotionInfoVO;
      Taro.navigateTo({
        url: `/packages/marketing/activityGoodsList/index?source=DOORSILLREMAIN&promotionId=${promotionId}&doorSillRemain=${doorSillRemain}&desc=${description}&tag=${
          tagText?.[0] || tag
        }`,
      });
    }
  }, [data]);

  // 配送类型没满足起送
  const isDeliveryNotStart = useMemo(() => {
    return +diffAmt > 0 && +cartDeliveryType === 0;
  }, [diffAmt, cartDeliveryType]);

  const calcCouponEffect = ({tabType, discountAmount, couponInfoVO}) => {
    let effect = '';
    const discount = fenToYuanSmart(discountAmount)
    const couponType = couponInfoVO?.couponVO?.couponType;
    switch (tabType) {
      case 2:
        effect = (
          <>
            可减
            <Text className={styles.redText}>{discount}</Text>
            元配送费
          </>
        );
        break;
      case 3:
        if (couponType === 1) {
          effect = (
            <>
              用券减
              <Text className={styles.redText}>{discount}</Text>元
            </>
          );
        }
        if (couponType === 2) {
          effect = (
            <>
              用券享
              <Text className={styles.redText}>{discountAmount}</Text>折
            </>
          );
        }
        if (couponType === 3) {
          effect = (
            <>
              用券减
              <Text className={styles.redText}>{discount}</Text>
              元配送费用
            </>
          );
        }
        break;
      default:
        break;
    }
    return effect;
  };

  const showDesc = useMemo(() => {
    if (isDeliveryNotStart) {
      return '到店自取免起送';
    }
    if (!collectData) return '';
    // 门槛差价
    const unitType =
      collectData.nextTab?.couponInfoVO?.couponVO?.promotion?.condition?.type;
    const promotionUnit = PromotionUnitMap[unitType];
    const promotionCondition =
      unitType === 1
        ? fenToYuanSmart(collectData.nextTab?.thresholdDifference, true)
        : collectData.nextTab?.thresholdDifference;
    const condition = `再买${promotionCondition}${promotionUnit}`;
    // 券效果
    const effect = calcCouponEffect(
      collectData.nextTab,
    );
    return (
      <>
        {condition}，{effect}
      </>
    );
  }, [isDeliveryNotStart, collectData]);

  const handleChangeType = (e) => {
    e.stopPropagation();
    if (isDeliveryNotStart) {
      onChangeCartType?.(1); // 切换到自提
    } else {
      handleClick();
    }
  };

  return (
    <View
      className={classNames(
        styles.box,
        styles.addPromotionBg,
        !isTab ? styles.boxIsNotTab : '',
      )}
      onClick={() => {
        onClick();
        clickOperateReport('去凑单');
      }}
    >
      <View className={styles.left}>
        <Image
          className={styles.tag}
          src={getImage('cart/add-order-tag.png')}
        ></Image>
        <View className={styles.text}>{showDesc}</View>
      </View>
      <Button className={styles.checkBtn} onClick={handleChangeType}>
        {isDeliveryNotStart ? '去自取' : '去凑单'}
      </Button>
    </View>
  );
};

export default AddPromotionBar;
